import { endpoint } from './app/config/endpoints'
import { Endpoint } from './app/endpoints/endpoint'

const client = new Endpoint(endpoint.parse({ name: 'Test', http: 'https://rpc.solanatracker.io/public?advancedTx=true' }))

await client['enableWarmupIfSupported']()

setTimeout(() => client.sendRawWithLimiter(), 5000)
setTimeout(() => client.sendRawWithLimiter(), 15_000)
setTimeout(() => client.sendRawWithLimiter(), 35_000)
