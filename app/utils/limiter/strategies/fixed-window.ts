import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface FixedWindowStrategyConfig {
    capacity: number
    window: number
}

export class FixedWindowStrategy extends Strategy<FixedWindowStrategyConfig> {
    public isLimited = false

    protected readonly bottleneck: Bottleneck
    protected readonly createdAt: number

    public constructor(config: FixedWindowStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
        this.createdAt = Date.now()
    }

    public async schedule<T>(fn: () => Awaitable<T>, { weight = 1, priority = 5 }: LimiterScheduleOptions = {}) {
        return this.bottleneck.schedule({ weight, priority }, async () => fn())
    }

    protected createBottleneck(config: FixedWindowStrategyConfig) {
        const bottleneck = new Bottleneck({
            reservoir: config.capacity,
            reservoirRefreshInterval: config.window,
            reservoirRefreshAmount: config.capacity,
            minTime: 0,
        })

        bottleneck.on('error', (error) => this.emitter.emit('error', error))
        bottleneck.on('depleted', () => this.handleDepleted(config))
        bottleneck.on('executing', () => this.handleExecuting())

        return bottleneck
    }

    protected handleDepleted(config: FixedWindowStrategyConfig) {
        if (this.isLimited) {
            return
        }

        this.isLimited = true

        const elapsed = Date.now() - this.createdAt
        const currentWindowEnd = this.createdAt + (Math.ceil(elapsed / config.window) * config.window)
        const until = new Date(currentWindowEnd)

        this.emitter.emit('limit', until, {})
    }

    protected handleExecuting() {
        this.isLimited = false
    }
}
