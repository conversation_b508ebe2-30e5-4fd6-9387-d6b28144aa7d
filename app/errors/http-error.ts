import { notNullish } from '@kdt310722/utils/common'
import { BaseError, type BaseErrorOptions } from '@kdt310722/utils/error'
import { isClientErrorStatus, parseHttpStatus } from '../utils/requests'

export interface HttpErrorOptions extends BaseErrorOptions {
    statusCode?: number
    statusMessage?: string
}

export class HttpError extends BaseError {
    public declare readonly statusCode?: number
    public declare readonly statusMessage?: string
    public declare readonly response?: unknown

    public constructor(message?: string, { statusCode, statusMessage, ...options }: HttpErrorOptions = {}) {
        super(message, options)

        if (notNullish(statusCode)) {
            this.withStatusCode(statusCode)
        }

        if (notNullish(statusMessage)) {
            this.withStatusMessage(statusMessage)
        }
    }

    public isClientError() {
        return notNullish(this.statusCode) && isClientErrorStatus(this.statusCode)
    }

    public withStatus(status: string): this {
        const { code, message } = parseHttpStatus(status)

        this.withStatusCode(code)
        this.withStatusMessage(message)

        return this
    }

    public withStatusCode(statusCode: number) {
        return this.withValue('statusCode', statusCode)
    }

    public withStatusMessage(statusMessage: string) {
        return this.withValue('statusMessage', statusMessage)
    }

    public withResponse(response: unknown) {
        return this.withValue('response', response)
    }
}
