import { highlight, type LogLevelType, message } from '@kdt310722/logger'
import { type Fn, tap } from '@kdt310722/utils/function'
import { BaseEndpoint } from './base-endpoint'
import { DEFAULT_WARMUP_CONNECTIONS, WARMUP_REQUEST } from './constants'

export class EndpointWithWarmup extends BaseEndpoint {
    protected warmupTimer?: NodeJS.Timeout
    protected isWarmingUp = false
    protected warmupConnectionsHandler?: Fn
    protected lastWarmupTime?: number

    protected get warmupConnections() {
        return this.config.http.warmup.connections ?? this.config.http.maxRequestsPerSecond ?? DEFAULT_WARMUP_CONNECTIONS
    }

    protected get minimumFreeConnections() {
        return this.config.http.warmup.minFreeConnections ?? Math.floor(this.warmupConnections / 2)
    }

    protected stopWarmup() {
        if (this.warmupTimer) {
            clearInterval(this.warmupTimer)
            this.warmupTimer = undefined
        }

        if (this.warmupConnectionsHandler) {
            this.sender.off('connections', this.warmupConnectionsHandler)
            this.warmupConnectionsHandler = undefined
        }
    }

    protected async enableWarmupIfSupported() {
        if (!this.config.http.warmup.enabled) {
            return
        }

        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Checking if keep-alive is supported for endpoint ${highlight(this.name)}...`))
        const keepAliveSupported = await this.isKeepAliveSupported()

        if (keepAliveSupported) {
            await this.warmup().then(() => this.warmupTimer = setInterval(() => this.warmup('debug'), this.config.http.warmup.interval))

            this.sender.on('connections', this.warmupConnectionsHandler = () => {
                if (this.sender.freeConnections < this.minimumFreeConnections) {
                    this.logger.warn(`Free connections for endpoint ${highlight(this.name)} is below minimum threshold (${highlight(this.sender.freeConnections)} < ${highlight(this.minimumFreeConnections)})`)

                    if (this.lastWarmupTime && Date.now() - this.lastWarmupTime < this.config.http.warmup.circuitBreakerThreshold) {
                        this.logger.warn(`Maybe endpoint ${highlight(this.name)} is not support keep-alive, stopping warmup!`)
                        this.stopWarmup()
                    } else {
                        this.warmup('info', this.minimumFreeConnections - this.sender.freeConnections).catch((error) => this.logger.error(`Failed to warmup endpoint ${highlight(this.name)}`, error))
                    }
                }
            })
        } else {
            this.logger.stopTimer(timer, 'info', `Keep-alive is not supported for endpoint ${highlight(this.name)}, warmup is disabled!`)
        }
    }

    protected async isKeepAliveSupported(count = 5) {
        let connections = 0
        let handler: Fn

        this.sender.on('connections', handler = (count) => {
            connections = Math.max(count, connections)
        })

        try {
            for (let i = 0; i < count; i++) {
                const response = await this.sendWarmupRequest().catch(() => null)

                if (response?.headers['connection'] === 'keep-alive') {
                    return tap(true, () => this.logger.debug(message(() => `Keep-alive for endpoint ${highlight(this.name)} is supported by 'connection: keep-alive' header`)))
                }
            }

            if (connections < count) {
                return tap(true, () => this.logger.debug(message(() => `Keep-alive for endpoint ${highlight(this.name)} is supported by connection count (${highlight(connections)} < ${highlight(count)})`)))
            }
        } finally {
            this.sender.off('connections', handler)
        }

        return false
    }

    protected async warmup(logLevel: LogLevelType = 'info', connections_?: number) {
        if (this.isWarmingUp) {
            return
        }

        this.isWarmingUp = true
        this.lastWarmupTime = Date.now()

        try {
            const promises: Array<Promise<unknown>> = []
            const connections = connections_ ?? this.warmupConnections
            const timer = tap(this.logger.createTimer(), () => this.logger.log(logLevel, `Warming up endpoint ${highlight(this.name)} with ${highlight(connections)} connections...`))

            for (let i = 0; i < connections; i++) {
                promises.push(this.sendWarmupRequest().catch((error) => this.logger.warn(`Warmup request for endpoint ${highlight(this.name)} failed`, error, { index: i })))
            }

            await Promise.all(promises).then(() => this.logger.stopTimer(timer, logLevel, `Warmup for endpoint ${highlight(this.name)} completed! Current free connections: ${highlight(this.sender.freeConnections)} / ${highlight(this.sender.connections)}`))
        } finally {
            this.isWarmingUp = false
        }
    }

    protected async sendWarmupRequest(signal?: AbortSignal) {
        return this.sendRawWithLimiter(this.config.http.warmup.body, { method: this.config.http.warmup.method, headers: this.config.http.warmup.headers, metadata: { [WARMUP_REQUEST]: true }, signal })
    }
}
